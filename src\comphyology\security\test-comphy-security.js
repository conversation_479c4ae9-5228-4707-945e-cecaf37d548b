/**
 * Comphy-Field Security Test Suite
 * Comprehensive testing for "Steal It, and It Turns to Dust" system
 * 
 * Author: <PERSON>, NovaFuse Technologies
 * Date: 2025-07-29
 */

const { ComphyDocumentFactory, demonstrateComphyLocking } = require('./ComphyLockedDocument');
const { CoherenceEnvelope } = require('./ComphyFieldSecuredData');
const { QuantumSelfDestruct } = require('./QuantumSelfDestruct');
const { ComphyFieldAuth } = require('./ComphyFieldAuth');

/**
 * Test Suite Runner
 */
class ComphySecurityTestSuite {
  constructor() {
    this.testResults = [];
    this.passedTests = 0;
    this.failedTests = 0;
  }

  /**
   * Run all tests
   */
  async runAllTests() {
    console.log('\n🧪 COMPHY-FIELD SECURITY TEST SUITE');
    console.log('===================================\n');

    // Core component tests
    await this.testCoherenceEnvelope();
    await this.testQuantumSelfDestruct();
    await this.testComphyFieldAuth();
    await this.testComphyLockedDocument();
    
    // Integration tests
    await this.testFieldAuthenticationFlow();
    await this.testSelfDestructTriggers();
    await this.testUnauthorizedAccessScenarios();
    
    // Security validation tests
    await this.testQuantumNoiseGeneration();
    await this.testFieldTopologyValidation();
    await this.testTemporalSignatureValidation();
    
    this.printTestSummary();
  }

  /**
   * Test Coherence Envelope
   */
  async testCoherenceEnvelope() {
    console.log('🔮 Testing Coherence Envelope...');
    
    try {
      const envelope = new CoherenceEnvelope({
        coherenceThreshold: 0.95,
        decoherenceTimeout: 30000
      });

      // Test 1: Data encryption
      const testData = "Secret NovaFuse quantum specifications";
      const encrypted = envelope.encryptData(testData, { test: true });
      
      this.assert(encrypted.envelope_id, 'Envelope ID generated');
      this.assert(encrypted.encrypted_data, 'Data encrypted');
      this.assert(encrypted.field_topology_hash, 'Field topology hash created');
      this.assert(encrypted.quantum_noise_key, 'Quantum noise key generated');

      // Test 2: Authorized decryption
      const fieldContext = {
        field_signature: encrypted.field_topology_hash,
        coherence_level: 0.97
      };
      
      const decrypted = envelope.decryptData(encrypted, fieldContext);
      this.assert(decrypted.success, 'Authorized decryption successful');
      this.assert(decrypted.data === testData, 'Decrypted data matches original');

      // Test 3: Unauthorized decryption (should trigger decoherence)
      const badFieldContext = {
        field_signature: 'INVALID_SIGNATURE',
        coherence_level: 0.1
      };
      
      const badDecrypt = envelope.decryptData(encrypted, badFieldContext);
      this.assert(!badDecrypt.success, 'Unauthorized decryption blocked');
      this.assert(badDecrypt.decoherence_reason, 'Decoherence reason provided');
      this.assert(badDecrypt.data !== testData, 'Original data not returned');

      envelope.destroy();
      this.recordTest('Coherence Envelope', true);
      
    } catch (error) {
      this.recordTest('Coherence Envelope', false, error.message);
    }
  }

  /**
   * Test Quantum Self-Destruct
   */
  async testQuantumSelfDestruct() {
    console.log('💥 Testing Quantum Self-Destruct...');
    
    try {
      const selfDestruct = new QuantumSelfDestruct({
        coherenceThreshold: 0.95,
        emergencyTimeout: 5000
      });

      // Test 1: Authorized session registration
      const session = selfDestruct.registerAuthorizedSession('test-session', {
        field_signature: 'VALID_FIELD_001',
        coherence_level: 0.97
      });
      
      this.assert(session.session_id, 'Session ID generated');
      this.assert(session.entanglement_key, 'Entanglement key created');

      // Test 2: Authorized access monitoring
      const authorizedAccess = selfDestruct.monitorAccessAttempt('access-001', {
        session_id: session.session_id,
        field_context: {
          field_signature: 'VALID_FIELD_001',
          coherence_level: 0.97,
          resonance_frequency: 314.159
        },
        source_signature: 'authorized_system'
      });
      
      this.assert(authorizedAccess.authorized, 'Authorized access allowed');
      this.assert(authorizedAccess.coherence_level > 0.9, 'High coherence maintained');

      // Test 3: Unauthorized access (should trigger self-destruct)
      const unauthorizedAccess = selfDestruct.monitorAccessAttempt('access-002', {
        session_id: 'INVALID_SESSION',
        field_context: {
          field_signature: 'INVALID_FIELD',
          coherence_level: 0.1
        },
        source_signature: 'unknown_system'
      });
      
      this.assert(!unauthorizedAccess.authorized, 'Unauthorized access blocked');
      this.assert(unauthorizedAccess.self_destruct_triggered, 'Self-destruct triggered');
      this.assert(unauthorizedAccess.quantum_noise, 'Quantum noise generated');

      selfDestruct.destroy();
      this.recordTest('Quantum Self-Destruct', true);
      
    } catch (error) {
      this.recordTest('Quantum Self-Destruct', false, error.message);
    }
  }

  /**
   * Test Comphy Field Authentication
   */
  async testComphyFieldAuth() {
    console.log('🔑 Testing Comphy Field Authentication...');
    
    try {
      const fieldAuth = new ComphyFieldAuth({
        resonanceThreshold: 0.75,
        temporalWindow: 30000
      });

      // Test 1: Field node registration
      const nodeResult = fieldAuth.registerFieldNode({
        position: { x: 0.5, y: 0.5, z: 0.5 },
        node_type: 'primary',
        security_level: 'high',
        authorized_entities: ['test-entity']
      });
      
      this.assert(nodeResult.node_id, 'Field node ID generated');
      this.assert(nodeResult.field_signature, 'Field signature created');
      this.assert(nodeResult.resonance_frequency > 0, 'Resonance frequency assigned');

      // Test 2: Field authentication initiation
      const fieldContext = {
        field_readings: {
          strength: 0.082,
          coherence_pattern: {
            phi_alignment: true,
            pi_resonance: true,
            trinity_structure: true
          },
          resonance_frequency: nodeResult.resonance_frequency
        }
      };
      
      const authInit = await fieldAuth.initiateFieldAuth('test-entity', fieldContext);
      this.assert(authInit.success, 'Field auth initiation successful');
      this.assert(authInit.challenge, 'Challenge generated');
      this.assert(authInit.field_requirements, 'Field requirements provided');

      // Test 3: Field authentication completion
      const resonanceResponse = {
        resonance_signature: {
          frequency: nodeResult.resonance_frequency,
          amplitude: 0.082,
          phase: Math.PI
        },
        field_context: {
          coherence_level: 0.95,
          current_readings: fieldContext.field_readings,
          stability_readings: { variance: 0.02 }
        },
        field_signature: nodeResult.field_signature
      };
      
      const authComplete = await fieldAuth.completeFieldAuth(authInit.attempt_id, resonanceResponse);
      this.assert(authComplete.success, 'Field auth completion successful');
      this.assert(authComplete.session_id, 'Session ID provided');
      this.assert(authComplete.field_token, 'Field token generated');

      fieldAuth.destroy();
      this.recordTest('Comphy Field Authentication', true);
      
    } catch (error) {
      this.recordTest('Comphy Field Authentication', false, error.message);
    }
  }

  /**
   * Test Comphy Locked Document
   */
  async testComphyLockedDocument() {
    console.log('📄 Testing Comphy Locked Document...');
    
    try {
      const factory = new ComphyDocumentFactory();
      const testContent = "Top Secret NovaFuse Document Content";

      // Test 1: Document creation
      const docResult = await factory.createDocument(testContent, {
        title: 'Test Document',
        classification: 'SECRET',
        coherenceThreshold: 0.95
      });
      
      this.assert(docResult.document_id, 'Document ID generated');
      this.assert(docResult.comphy_document, 'Comphy document created');
      this.assert(docResult.comphy_document.encrypted_package, 'Document encrypted');

      // Test 2: Authorized access
      const authorizedContext = {
        entity_id: 'test-user',
        field_readings: {
          field_signature: 'AUTHORIZED_FIELD_001',
          coherence_level: 0.97,
          resonance_frequency: 314.159,
          field_strength: 0.082
        }
      };
      
      const authorizedAccess = await factory.accessDocument(
        docResult.document_id,
        docResult.comphy_document,
        authorizedContext
      );
      
      this.assert(authorizedAccess.success, 'Authorized access successful');
      this.assert(authorizedAccess.content === testContent, 'Content retrieved correctly');
      this.assert(authorizedAccess.field_authenticated, 'Field authentication confirmed');

      // Test 3: Unauthorized access
      const unauthorizedContext = {
        entity_id: 'hacker',
        field_readings: {
          field_signature: 'FAKE_SIGNATURE',
          coherence_level: 0.1,
          resonance_frequency: 50,
          field_strength: 0.001
        }
      };
      
      const unauthorizedAccess = await factory.accessDocument(
        docResult.document_id,
        docResult.comphy_document,
        unauthorizedContext
      );
      
      this.assert(!unauthorizedAccess.success, 'Unauthorized access blocked');
      this.assert(unauthorizedAccess.self_destruct_triggered, 'Self-destruct triggered');
      this.assert(unauthorizedAccess.data !== testContent, 'Original content not returned');

      // Test 4: Email copy attempt
      const emailResult = await factory.simulateEmailCopy(
        docResult.document_id,
        docResult.comphy_document,
        { recipient: '<EMAIL>', method: 'email' }
      );
      
      this.assert(!emailResult.success, 'Email copy blocked');
      this.assert(emailResult.self_destruct_triggered, 'Self-destruct on email copy');
      this.assert(emailResult.quantum_noise, 'Quantum noise generated');

      this.recordTest('Comphy Locked Document', true);
      
    } catch (error) {
      this.recordTest('Comphy Locked Document', false, error.message);
    }
  }

  /**
   * Test field authentication flow
   */
  async testFieldAuthenticationFlow() {
    console.log('🔄 Testing Field Authentication Flow...');
    
    try {
      // This test validates the complete authentication workflow
      const factory = new ComphyDocumentFactory();
      const testDoc = await factory.createDocument("Authentication Test Content");
      
      // Valid field context
      const validContext = {
        entity_id: '<EMAIL>',
        field_readings: {
          field_signature: 'NOVAFUSE_SECURE_FIELD_001',
          coherence_level: 0.98,
          resonance_frequency: 314.159,
          field_strength: 0.082,
          temporal_stability: 0.95
        },
        source_signature: 'authorized_workstation',
        location: 'NovaFuse_Secure_Lab'
      };
      
      const access = await factory.accessDocument(
        testDoc.document_id,
        testDoc.comphy_document,
        validContext
      );
      
      this.assert(access.success, 'Complete authentication flow successful');
      this.assert(access.access_info.session_id, 'Session established');
      this.assert(access.access_info.field_authenticated, 'Field authentication confirmed');
      
      this.recordTest('Field Authentication Flow', true);
      
    } catch (error) {
      this.recordTest('Field Authentication Flow', false, error.message);
    }
  }

  /**
   * Test self-destruct triggers
   */
  async testSelfDestructTriggers() {
    console.log('💣 Testing Self-Destruct Triggers...');
    
    try {
      const factory = new ComphyDocumentFactory();
      const testDoc = await factory.createDocument("Self-Destruct Test Content");
      
      // Test various trigger conditions
      const triggerScenarios = [
        {
          name: 'No Field Presence',
          context: {
            entity_id: 'test-user',
            field_readings: {} // Empty field readings
          }
        },
        {
          name: 'Low Coherence',
          context: {
            entity_id: 'test-user',
            field_readings: {
              field_signature: 'VALID_FIELD',
              coherence_level: 0.1, // Below threshold
              resonance_frequency: 314.159,
              field_strength: 0.082
            }
          }
        },
        {
          name: 'Invalid Field Signature',
          context: {
            entity_id: 'test-user',
            field_readings: {
              field_signature: 'INVALID_SIGNATURE',
              coherence_level: 0.97,
              resonance_frequency: 314.159,
              field_strength: 0.082
            }
          }
        }
      ];
      
      let triggersWorking = 0;
      
      for (const scenario of triggerScenarios) {
        const result = await factory.accessDocument(
          testDoc.document_id,
          testDoc.comphy_document,
          scenario.context
        );
        
        if (!result.success && result.self_destruct_triggered) {
          triggersWorking++;
        }
      }
      
      this.assert(triggersWorking === triggerScenarios.length, 'All self-destruct triggers working');
      this.recordTest('Self-Destruct Triggers', true);
      
    } catch (error) {
      this.recordTest('Self-Destruct Triggers', false, error.message);
    }
  }

  /**
   * Test unauthorized access scenarios
   */
  async testUnauthorizedAccessScenarios() {
    console.log('🚫 Testing Unauthorized Access Scenarios...');
    
    try {
      const factory = new ComphyDocumentFactory();
      const testDoc = await factory.createDocument("Unauthorized Access Test");
      
      // Test multiple unauthorized access attempts
      const unauthorizedAttempts = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
      ];
      
      let blockedAttempts = 0;
      
      for (const entityId of unauthorizedAttempts) {
        const result = await factory.accessDocument(
          testDoc.document_id,
          testDoc.comphy_document,
          {
            entity_id: entityId,
            field_readings: {
              field_signature: 'FAKE_FIELD',
              coherence_level: Math.random() * 0.5, // Random low coherence
              resonance_frequency: Math.random() * 100,
              field_strength: Math.random() * 0.01
            }
          }
        );
        
        if (!result.success) {
          blockedAttempts++;
        }
      }
      
      this.assert(blockedAttempts === unauthorizedAttempts.length, 'All unauthorized attempts blocked');
      this.recordTest('Unauthorized Access Scenarios', true);
      
    } catch (error) {
      this.recordTest('Unauthorized Access Scenarios', false, error.message);
    }
  }

  /**
   * Test quantum noise generation
   */
  async testQuantumNoiseGeneration() {
    console.log('🌪️ Testing Quantum Noise Generation...');
    
    try {
      const selfDestruct = new QuantumSelfDestruct();
      
      // Trigger self-destruct to generate noise
      const destructResult = selfDestruct.triggerSelfDestruct('test-access', 'test_trigger');
      
      this.assert(!destructResult.success, 'Self-destruct returns failure');
      this.assert(destructResult.data, 'Quantum noise data generated');
      this.assert(destructResult.data.length > 100, 'Sufficient noise generated');
      this.assert(destructResult.quantum_noise, 'Quantum noise flag set');
      
      // Verify noise is actually random
      const noise1 = selfDestruct.triggerSelfDestruct('test-access-2', 'test_trigger_2');
      const noise2 = selfDestruct.triggerSelfDestruct('test-access-3', 'test_trigger_3');
      
      this.assert(noise1.data !== noise2.data, 'Quantum noise is unique each time');
      
      selfDestruct.destroy();
      this.recordTest('Quantum Noise Generation', true);
      
    } catch (error) {
      this.recordTest('Quantum Noise Generation', false, error.message);
    }
  }

  /**
   * Test field topology validation
   */
  async testFieldTopologyValidation() {
    console.log('🗺️ Testing Field Topology Validation...');
    
    try {
      const envelope = new CoherenceEnvelope();
      
      // Test field topology generation
      const topology1 = envelope.generateFieldTopology();
      const topology2 = envelope.generateFieldTopology();
      
      this.assert(topology1, 'Field topology generated');
      this.assert(topology1 !== topology2, 'Each topology is unique');
      this.assert(topology1.length === 64, 'Topology hash is correct length'); // SHA-256 hex
      
      envelope.destroy();
      this.recordTest('Field Topology Validation', true);
      
    } catch (error) {
      this.recordTest('Field Topology Validation', false, error.message);
    }
  }

  /**
   * Test temporal signature validation
   */
  async testTemporalSignatureValidation() {
    console.log('⏰ Testing Temporal Signature Validation...');
    
    try {
      const envelope = new CoherenceEnvelope();
      
      // Test temporal signature generation
      const signature1 = envelope.generateTemporalSignature();
      
      // Wait a moment
      await new Promise(resolve => setTimeout(resolve, 10));
      
      const signature2 = envelope.generateTemporalSignature();
      
      this.assert(signature1.base_timestamp, 'Base timestamp generated');
      this.assert(signature1.quantum_noise, 'Quantum noise component present');
      this.assert(signature1.coherence_phase, 'Coherence phase calculated');
      this.assert(signature1.temporal_hash, 'Temporal hash generated');
      
      this.assert(signature1.base_timestamp !== signature2.base_timestamp, 'Timestamps are different');
      this.assert(signature1.temporal_hash !== signature2.temporal_hash, 'Temporal hashes are unique');
      
      envelope.destroy();
      this.recordTest('Temporal Signature Validation', true);
      
    } catch (error) {
      this.recordTest('Temporal Signature Validation', false, error.message);
    }
  }

  /**
   * Assert helper
   */
  assert(condition, message) {
    if (!condition) {
      throw new Error(`Assertion failed: ${message}`);
    }
    console.log(`  ✅ ${message}`);
  }

  /**
   * Record test result
   */
  recordTest(testName, passed, error = null) {
    const result = {
      name: testName,
      passed: passed,
      error: error,
      timestamp: Date.now()
    };
    
    this.testResults.push(result);
    
    if (passed) {
      this.passedTests++;
      console.log(`✅ ${testName}: PASSED\n`);
    } else {
      this.failedTests++;
      console.log(`❌ ${testName}: FAILED - ${error}\n`);
    }
  }

  /**
   * Print test summary
   */
  printTestSummary() {
    console.log('\n📊 TEST SUMMARY');
    console.log('===============');
    console.log(`Total Tests: ${this.testResults.length}`);
    console.log(`Passed: ${this.passedTests}`);
    console.log(`Failed: ${this.failedTests}`);
    console.log(`Success Rate: ${((this.passedTests / this.testResults.length) * 100).toFixed(1)}%`);
    
    if (this.failedTests > 0) {
      console.log('\n❌ FAILED TESTS:');
      this.testResults
        .filter(test => !test.passed)
        .forEach(test => console.log(`  - ${test.name}: ${test.error}`));
    }
    
    console.log(`\n${this.failedTests === 0 ? '🎉 ALL TESTS PASSED!' : '⚠️ SOME TESTS FAILED'}`);
  }
}

/**
 * Run tests if called directly
 */
if (require.main === module) {
  const testSuite = new ComphySecurityTestSuite();
  
  console.log('Choose test mode:');
  console.log('1. Run full test suite');
  console.log('2. Run demonstration');
  
  const mode = process.argv[2] || '1';
  
  if (mode === '2' || mode === 'demo') {
    demonstrateComphyLocking().catch(console.error);
  } else {
    testSuite.runAllTests().catch(console.error);
  }
}

module.exports = {
  ComphySecurityTestSuite
};
