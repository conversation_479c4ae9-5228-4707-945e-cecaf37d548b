<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NovaFuse Demo Central Hub - Standalone</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: #ffffff;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            backdrop-filter: blur(10px);
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #00d4ff, #ff00ff, #ffff00);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .audience-filter {
            margin: 20px 0;
            text-align: center;
        }
        
        .audience-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: #fff;
            padding: 8px 16px;
            margin: 5px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .audience-btn:hover, .audience-btn.active {
            background: #00d4ff;
            color: #000;
        }
        
        .demo-categories {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
        }
        
        .category {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .category h3 {
            color: #00d4ff;
            margin-bottom: 20px;
            font-size: 1.3em;
        }
        
        .demo-item {
            background: rgba(255, 255, 255, 0.03);
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            border: 1px solid rgba(255, 255, 255, 0.1);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .demo-item:hover {
            background: rgba(255, 255, 255, 0.08);
            transform: translateY(-2px);
        }
        
        .demo-name {
            font-weight: bold;
            color: #fff;
            margin-bottom: 5px;
        }
        
        .demo-description {
            color: #ccc;
            font-size: 0.9em;
            margin-bottom: 8px;
        }
        
        .demo-audience {
            font-size: 0.8em;
            color: #00d4ff;
        }
        
        .launch-btn {
            background: linear-gradient(45deg, #00ff88, #00d4ff);
            color: #000;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            float: right;
            margin-top: 10px;
        }
        
        .launch-btn:hover {
            transform: scale(1.05);
        }
        
        .stats {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
        }
        
        .featured-demo {
            background: rgba(255, 215, 0, 0.1);
            border: 2px solid #ffd700;
            animation: featuredPulse 2s infinite;
        }
        
        @keyframes featuredPulse {
            0%, 100% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.3); }
            50% { box-shadow: 0 0 40px rgba(255, 215, 0, 0.6); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎬 NovaFuse Demo Central Hub</h1>
            <p>Every Working Demo and Dashboard in One Place</p>
            
            <div class="audience-filter">
                <button class="audience-btn active" onclick="filterByAudience('all')">All Demos</button>
                <button class="audience-btn" onclick="filterByAudience('investors')">Investors</button>
                <button class="audience-btn" onclick="filterByAudience('enterprise')">Enterprise</button>
                <button class="audience-btn" onclick="filterByAudience('technical')">Technical</button>
                <button class="audience-btn" onclick="filterByAudience('researchers')">Researchers</button>
                <button class="audience-btn" onclick="filterByAudience('financial')">Financial</button>
            </div>
        </div>
        
        <div class="stats" id="demoStats">
            <h3>📊 Demo Statistics</h3>
            <p><strong>50+</strong> working demonstrations available</p>
            <p>Filtered for: <strong>All Audiences</strong></p>
        </div>
        
        <div class="demo-categories" id="demoCategories">
            <!-- Security Demos -->
            <div class="category" data-audiences="investors,enterprise,government,technical">
                <h3>🔮 Quantum Security Demos</h3>
                
                <div class="demo-item featured-demo" onclick="launchDemo('comphy-field-demo.html')">
                    <div class="demo-name">🏆 Comphy-Field Self-Destructing Data System</div>
                    <div class="demo-description">Revolutionary "Steal It, and It Turns to Dust" security</div>
                    <div class="demo-audience">Audience: investors, enterprise, government</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('comphy-field-demo.html')">Launch</button>
                </div>
                
                <div class="demo-item" onclick="launchDemo('../src/novashield/NovaShield_TraceGuard_MVP.py')">
                    <div class="demo-name">NovaShield TraceGuard</div>
                    <div class="demo-description">Advanced threat detection and response</div>
                    <div class="demo-audience">Audience: technical, security</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('../src/novashield/NovaShield_TraceGuard_MVP.py')">Launch</button>
                </div>
                
                <div class="demo-item" onclick="launchDemo('../public/novavision-security-demo.html')">
                    <div class="demo-name">NovaVision Security</div>
                    <div class="demo-description">Comprehensive security monitoring</div>
                    <div class="demo-audience">Audience: enterprise, technical</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('../public/novavision-security-demo.html')">Launch</button>
                </div>
            </div>
            
            <!-- AI & Consciousness Demos -->
            <div class="category" data-audiences="researchers,technical,government">
                <h3>🧠 AI & Consciousness Demos</h3>
                
                <div class="demo-item" onclick="launchDemo('../coherence-reality-systems/ai-alignment-demo/')">
                    <div class="demo-name">NovaAlign Studio</div>
                    <div class="demo-description">AI alignment and consciousness validation</div>
                    <div class="demo-audience">Audience: researchers, technical, government</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('../coherence-reality-systems/ai-alignment-demo/')">Launch</button>
                </div>
                
                <div class="demo-item" onclick="launchDemo('../trinity_visualization/')">
                    <div class="demo-name">Trinity Consciousness Engine</div>
                    <div class="demo-description">Triadic consciousness processing</div>
                    <div class="demo-audience">Audience: researchers, technical</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('../trinity_visualization/')">Launch</button>
                </div>
                
                <div class="demo-item" onclick="launchDemo('../advanced_consciousness_demo.py')">
                    <div class="demo-name">Advanced Consciousness Demo</div>
                    <div class="demo-description">Comprehensive consciousness testing</div>
                    <div class="demo-audience">Audience: researchers, technical</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('../advanced_consciousness_demo.py')">Launch</button>
                </div>

                <div class="demo-item" onclick="launchDemo('../N3C_Consciousness_Simulation.py')">
                    <div class="demo-name">N3C Consciousness Simulation</div>
                    <div class="demo-description">Neural-3-Consciousness simulation engine</div>
                    <div class="demo-audience">Audience: researchers, technical</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('../N3C_Consciousness_Simulation.py')">Launch</button>
                </div>

                <div class="demo-item" onclick="launchDemo('../trinity_consciousness_simulation.py')">
                    <div class="demo-name">Trinity Consciousness Simulation</div>
                    <div class="demo-description">Triadic consciousness processing simulation</div>
                    <div class="demo-audience">Audience: researchers, technical</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('../trinity_consciousness_simulation.py')">Launch</button>
                </div>

                <div class="demo-item" onclick="launchDemo('../quantum_consciousness_firewall.py')">
                    <div class="demo-name">Quantum Consciousness Firewall</div>
                    <div class="demo-description">Consciousness-based security firewall</div>
                    <div class="demo-audience">Audience: researchers, technical, security</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('../quantum_consciousness_firewall.py')">Launch</button>
                </div>
            </div>
            
            <!-- Enterprise & Business Demos -->
            <div class="category" data-audiences="enterprise,financial,investors">
                <h3>🏢 Enterprise & Business Demos</h3>
                
                <div class="demo-item" onclick="launchDemo('../NOVAACTUARY-DEMO-COMPLETE.md')">
                    <div class="demo-name">NovaActuary Risk Assessment</div>
                    <div class="demo-description">AI-powered insurance and risk analysis</div>
                    <div class="demo-audience">Audience: enterprise, financial, investors</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('../NOVAACTUARY-DEMO-COMPLETE.md')">Launch</button>
                </div>
                
                <div class="demo-item" onclick="launchDemo('partner-ecosystem.html')">
                    <div class="demo-name">Partner Ecosystem Portal</div>
                    <div class="demo-description">Strategic partnership management</div>
                    <div class="demo-audience">Audience: partners, enterprise</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('partner-ecosystem.html')">Launch</button>
                </div>
                
                <div class="demo-item" onclick="launchDemo('../NovaConnect-Testing-Summary.md')">
                    <div class="demo-name">NovaConnect Testing Suite</div>
                    <div class="demo-description">Universal API connectivity platform</div>
                    <div class="demo-audience">Audience: technical, enterprise</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('../NovaConnect-Testing-Summary.md')">Launch</button>
                </div>
            </div>
            
            <!-- Research & Development Demos -->
            <div class="category" data-audiences="researchers,academic">
                <h3>🔬 Research & Development Demos</h3>

                <div class="demo-item" onclick="launchDemo('../unified-field-theory.html')">
                    <div class="demo-name">UUFT Unified Field Theory</div>
                    <div class="demo-description">Universal physics simulations</div>
                    <div class="demo-audience">Audience: researchers, academic</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('../unified-field-theory.html')">Launch</button>
                </div>

                <div class="demo-item" onclick="launchDemo('../three_body_problem_solver.py')">
                    <div class="demo-name">Three Body Problem Solver</div>
                    <div class="demo-description">Revolutionary physics solution</div>
                    <div class="demo-audience">Audience: researchers, academic</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('../three_body_problem_solver.py')">Launch</button>
                </div>

                <div class="demo-item" onclick="launchDemo('../Mathematical_Consciousness_Proof.py')">
                    <div class="demo-name">Mathematical Consciousness Proof</div>
                    <div class="demo-description">Consciousness mathematics validation</div>
                    <div class="demo-audience">Audience: researchers, academic</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('../Mathematical_Consciousness_Proof.py')">Launch</button>
                </div>

                <div class="demo-item" onclick="launchDemo('../anti_gravity_oscillator.py')">
                    <div class="demo-name">Anti-Gravity Oscillator</div>
                    <div class="demo-description">Revolutionary anti-gravity technology</div>
                    <div class="demo-audience">Audience: researchers, academic</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('../anti_gravity_oscillator.py')">Launch</button>
                </div>

                <div class="demo-item" onclick="launchDemo('../UUFT_Dark_Field_Solution.py')">
                    <div class="demo-name">UUFT Dark Field Solution</div>
                    <div class="demo-description">Dark field physics breakthrough</div>
                    <div class="demo-audience">Audience: researchers, academic</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('../UUFT_Dark_Field_Solution.py')">Launch</button>
                </div>

                <div class="demo-item" onclick="launchDemo('../UUFT_Protein_Folding_Solution.py')">
                    <div class="demo-name">UUFT Protein Folding Solution</div>
                    <div class="demo-description">Protein folding with unified field theory</div>
                    <div class="demo-audience">Audience: researchers, academic</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('../UUFT_Protein_Folding_Solution.py')">Launch</button>
                </div>
            </div>
            
            <!-- Financial & Trading Demos -->
            <div class="category" data-audiences="financial,investors">
                <h3>💰 Financial & Trading Demos</h3>
                
                <div class="demo-item" onclick="launchDemo('../coherence-reality-systems/chaeonix-divine-dashboard/')">
                    <div class="demo-name">CHAEONIX Divine Dashboard</div>
                    <div class="demo-description">Consciousness-based trading platform</div>
                    <div class="demo-audience">Audience: financial, investors</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('../coherence-reality-systems/chaeonix-divine-dashboard/')">Launch</button>
                </div>
                
                <div class="demo-item" onclick="launchDemo('../src/wall_street_oracle/')">
                    <div class="demo-name">Wall Street Oracle</div>
                    <div class="demo-description">Predictive market analysis</div>
                    <div class="demo-audience">Audience: financial, investors</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('../src/wall_street_oracle/')">Launch</button>
                </div>
                
                <div class="demo-item" onclick="launchDemo('../revenue-calculator.md')">
                    <div class="demo-name">Revenue Calculator</div>
                    <div class="demo-description">Financial forecasting and projections</div>
                    <div class="demo-audience">Audience: investors, financial</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('../revenue-calculator.md')">Launch</button>
                </div>

                <div class="demo-item" onclick="launchDemo('../volatility_smile_test.py')">
                    <div class="demo-name">Volatility Smile Test</div>
                    <div class="demo-description">Advanced options pricing with sacred numerology</div>
                    <div class="demo-audience">Audience: financial, researchers</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('../volatility_smile_test.py')">Launch</button>
                </div>

                <div class="demo-item" onclick="launchDemo('../sacred_numerology_demo.py')">
                    <div class="demo-name">Sacred Numerology Demo</div>
                    <div class="demo-description">π-wave market calibration demonstration</div>
                    <div class="demo-audience">Audience: financial, researchers</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('../sacred_numerology_demo.py')">Launch</button>
                </div>

                <div class="demo-item" onclick="launchDemo('../clickbank_consciousness_domination.py')">
                    <div class="demo-name">ClickBank Consciousness Domination</div>
                    <div class="demo-description">Consciousness-based affiliate marketing</div>
                    <div class="demo-audience">Audience: financial, marketing</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('../clickbank_consciousness_domination.py')">Launch</button>
                </div>
            </div>
            
            <!-- HTML Dashboards & Visualizations -->
            <div class="category" data-audiences="technical,researchers,enterprise">
                <h3>📊 HTML Dashboards & Visualizations</h3>

                <div class="demo-item" onclick="launchDemo('../NECE-Enhanced-Dashboard.html')">
                    <div class="demo-name">NECE Enhanced Dashboard</div>
                    <div class="demo-description">Natural Emergent Chemistry Engine dashboard</div>
                    <div class="demo-audience">Audience: technical, researchers</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('../NECE-Enhanced-Dashboard.html')">Launch</button>
                </div>

                <div class="demo-item" onclick="launchDemo('../NovaFold-Enhanced-Dashboard.html')">
                    <div class="demo-name">NovaFold Enhanced Dashboard</div>
                    <div class="demo-description">Protein folding with consciousness guidance</div>
                    <div class="demo-audience">Audience: technical, researchers</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('../NovaFold-Enhanced-Dashboard.html')">Launch</button>
                </div>

                <div class="demo-item" onclick="launchDemo('../novafuse-master-control-hub.html')">
                    <div class="demo-name">NovaFuse Master Control Hub</div>
                    <div class="demo-description">Central command and control interface</div>
                    <div class="demo-audience">Audience: technical, enterprise</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('../novafuse-master-control-hub.html')">Launch</button>
                </div>

                <div class="demo-item" onclick="launchDemo('../novafuse-demo-selector.html')">
                    <div class="demo-name">NovaFuse Demo Selector</div>
                    <div class="demo-description">Interactive demo selection interface</div>
                    <div class="demo-audience">Audience: all</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('../novafuse-demo-selector.html')">Launch</button>
                </div>

                <div class="demo-item" onclick="launchDemo('../strategic-framework-viewer.html')">
                    <div class="demo-name">Strategic Framework Viewer</div>
                    <div class="demo-description">Interactive strategic architecture visualization</div>
                    <div class="demo-audience">Audience: executives, enterprise</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('../strategic-framework-viewer.html')">Launch</button>
                </div>

                <div class="demo-item" onclick="launchDemo('../alignment-architecture.html')">
                    <div class="demo-name">Alignment Architecture</div>
                    <div class="demo-description">AI alignment architecture visualization</div>
                    <div class="demo-audience">Audience: technical, researchers</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('../alignment-architecture.html')">Launch</button>
                </div>
            </div>

            <!-- Interactive Presentations -->
            <div class="category" data-audiences="investors,partners,enterprise,executives">
                <h3>🎮 Interactive Presentations</h3>
                
                <div class="demo-item" onclick="launchDemo('technology-roadmap.html')">
                    <div class="demo-name">Technology Roadmap</div>
                    <div class="demo-description">NovaFuse development timeline</div>
                    <div class="demo-audience">Audience: investors, partners, enterprise</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('technology-roadmap.html')">Launch</button>
                </div>
                
                <div class="demo-item" onclick="launchDemo('competitor-matrix.html')">
                    <div class="demo-name">Competitor Analysis</div>
                    <div class="demo-description">Market positioning matrix</div>
                    <div class="demo-audience">Audience: investors, enterprise</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('competitor-matrix.html')">Launch</button>
                </div>
                
                <div class="demo-item" onclick="launchDemo('../NovaFuse-Executive-Summary-20250720.html')">
                    <div class="demo-name">Executive Summary</div>
                    <div class="demo-description">High-level NovaFuse overview</div>
                    <div class="demo-audience">Audience: executives, investors</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('../NovaFuse-Executive-Summary-20250720.html')">Launch</button>
                </div>
            </div>

            <!-- Testing & Validation Suites -->
            <div class="category" data-audiences="technical,researchers">
                <h3>🧪 Testing & Validation Suites</h3>

                <div class="demo-item" onclick="launchDemo('../pi-coherence-master-test-suite.js')">
                    <div class="demo-name">π-Coherence Master Test Suite</div>
                    <div class="demo-description">Comprehensive π-coherence validation testing</div>
                    <div class="demo-audience">Audience: technical, researchers</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('../pi-coherence-master-test-suite.js')">Launch</button>
                </div>

                <div class="demo-item" onclick="launchDemo('../run_all_uuft_tests.py')">
                    <div class="demo-name">UUFT Complete Test Suite</div>
                    <div class="demo-description">Universal Unified Field Theory validation</div>
                    <div class="demo-audience">Audience: technical, researchers</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('../run_all_uuft_tests.py')">Launch</button>
                </div>

                <div class="demo-item" onclick="launchDemo('../trinity-day3-complete-test.js')">
                    <div class="demo-name">Trinity Complete Test</div>
                    <div class="demo-description">Complete Trinity system validation</div>
                    <div class="demo-audience">Audience: technical, researchers</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('../trinity-day3-complete-test.js')">Launch</button>
                </div>

                <div class="demo-item" onclick="launchDemo('../test_10k_memory_stress.py')">
                    <div class="demo-name">10K Memory Stress Test</div>
                    <div class="demo-description">High-performance memory validation</div>
                    <div class="demo-audience">Audience: technical</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('../test_10k_memory_stress.py')">Launch</button>
                </div>

                <div class="demo-item" onclick="launchDemo('../benchmark-comphyological-tensor-core.js')">
                    <div class="demo-name">Comphyological Tensor Core Benchmark</div>
                    <div class="demo-description">Tensor processing performance benchmarks</div>
                    <div class="demo-audience">Audience: technical</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('../benchmark-comphyological-tensor-core.js')">Launch</button>
                </div>
            </div>

            <!-- Specialized Applications -->
            <div class="category" data-audiences="technical,researchers,medical">
                <h3>🔬 Specialized Applications</h3>

                <div class="demo-item" onclick="launchDemo('../NovaFold_Live_Demo.py')">
                    <div class="demo-name">NovaFold Live Demo</div>
                    <div class="demo-description">Real-time protein folding demonstration</div>
                    <div class="demo-audience">Audience: technical, medical, researchers</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('../NovaFold_Live_Demo.py')">Launch</button>
                </div>

                <div class="demo-item" onclick="launchDemo('../earth_consciousness_dashboard.py')">
                    <div class="demo-name">Earth Consciousness Dashboard</div>
                    <div class="demo-description">Global consciousness monitoring system</div>
                    <div class="demo-audience">Audience: researchers, environmental</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('../earth_consciousness_dashboard.py')">Launch</button>
                </div>

                <div class="demo-item" onclick="launchDemo('../nova-intelligence-demo.py')">
                    <div class="demo-name">Nova Intelligence Demo</div>
                    <div class="demo-description">Comprehensive Nova component discovery</div>
                    <div class="demo-audience">Audience: technical, enterprise</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('../nova-intelligence-demo.py')">Launch</button>
                </div>

                <div class="demo-item" onclick="launchDemo('../examples/demo.js')">
                    <div class="demo-name">NovaFuse Three-Tier Demo</div>
                    <div class="demo-description">Physics, Transition, and Legacy tier demonstration</div>
                    <div class="demo-audience">Audience: technical, enterprise</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('../examples/demo.js')">Launch</button>
                </div>

                <div class="demo-item" onclick="launchDemo('../simple_nece_demo.py')">
                    <div class="demo-name">Simple NECE Demo</div>
                    <div class="demo-description">Natural Emergent Chemistry Engine basics</div>
                    <div class="demo-audience">Audience: technical, researchers</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('../simple_nece_demo.py')">Launch</button>
                </div>

                <div class="demo-item" onclick="launchDemo('../simple_ni_demo.py')">
                    <div class="demo-name">Simple NI Demo</div>
                    <div class="demo-description">Nova Intelligence basic demonstration</div>
                    <div class="demo-audience">Audience: technical</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('../simple_ni_demo.py')">Launch</button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        let currentAudience = 'all';
        
        function filterByAudience(audience) {
            currentAudience = audience;
            
            // Update button states
            document.querySelectorAll('.audience-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // Filter categories
            const categories = document.querySelectorAll('.category');
            let visibleDemos = 0;
            
            categories.forEach(category => {
                const audiences = category.getAttribute('data-audiences').split(',');
                const shouldShow = audience === 'all' || audiences.some(aud => aud.trim() === audience);
                
                if (shouldShow) {
                    category.style.display = 'block';
                    visibleDemos += category.querySelectorAll('.demo-item').length;
                } else {
                    category.style.display = 'none';
                }
            });
            
            // Update stats
            const totalDemos = document.querySelectorAll('.demo-item').length;
            document.getElementById('demoStats').innerHTML = `
                <h3>📊 Demo Statistics</h3>
                <p><strong>${visibleDemos}</strong> of <strong>${totalDemos}</strong> working demonstrations available</p>
                <p>Filtered for: <strong>${audience === 'all' ? 'All Audiences' : audience}</strong></p>
                <p>Categories: Security, AI/Consciousness, Enterprise, Research, Financial, Dashboards, Presentations, Testing, Specialized</p>
            `;
        }
        
        function launchDemo(demoPath) {
            if (demoPath.endsWith('.html')) {
                // Open HTML files directly
                window.open(demoPath, '_blank');
            } else if (demoPath.endsWith('.py')) {
                alert(`Python Demo: ${demoPath}\n\nTo run this demo, execute:\npython ${demoPath}`);
            } else if (demoPath.endsWith('.md')) {
                // Open markdown files (will depend on browser)
                window.open(demoPath, '_blank');
            } else if (demoPath.endsWith('/')) {
                // Directory - try to find index file
                const indexPath = demoPath + 'index.html';
                window.open(indexPath, '_blank');
            } else {
                alert(`Demo: ${demoPath}\n\nThis demo requires manual launch. Check the demo documentation for specific instructions.`);
            }
        }
    </script>
</body>
</html>
