<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NovaFuse Demo Central Hub - Standalone</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: #ffffff;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            backdrop-filter: blur(10px);
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #00d4ff, #ff00ff, #ffff00);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .audience-filter {
            margin: 20px 0;
            text-align: center;
        }
        
        .audience-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: #fff;
            padding: 8px 16px;
            margin: 5px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .audience-btn:hover, .audience-btn.active {
            background: #00d4ff;
            color: #000;
        }
        
        .demo-categories {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
        }
        
        .category {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .category h3 {
            color: #00d4ff;
            margin-bottom: 20px;
            font-size: 1.3em;
        }
        
        .demo-item {
            background: rgba(255, 255, 255, 0.03);
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            border: 1px solid rgba(255, 255, 255, 0.1);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .demo-item:hover {
            background: rgba(255, 255, 255, 0.08);
            transform: translateY(-2px);
        }
        
        .demo-name {
            font-weight: bold;
            color: #fff;
            margin-bottom: 5px;
        }
        
        .demo-description {
            color: #ccc;
            font-size: 0.9em;
            margin-bottom: 8px;
        }
        
        .demo-audience {
            font-size: 0.8em;
            color: #00d4ff;
        }
        
        .launch-btn {
            background: linear-gradient(45deg, #00ff88, #00d4ff);
            color: #000;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            float: right;
            margin-top: 10px;
        }
        
        .launch-btn:hover {
            transform: scale(1.05);
        }
        
        .stats {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
        }
        
        .featured-demo {
            background: rgba(255, 215, 0, 0.1);
            border: 2px solid #ffd700;
            animation: featuredPulse 2s infinite;
        }
        
        @keyframes featuredPulse {
            0%, 100% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.3); }
            50% { box-shadow: 0 0 40px rgba(255, 215, 0, 0.6); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎬 NovaFuse Demo Central Hub</h1>
            <p>Every Working Demo and Dashboard in One Place</p>
            
            <div class="audience-filter">
                <button class="audience-btn active" onclick="filterByAudience('all')">All Demos</button>
                <button class="audience-btn" onclick="filterByAudience('investors')">Investors</button>
                <button class="audience-btn" onclick="filterByAudience('enterprise')">Enterprise</button>
                <button class="audience-btn" onclick="filterByAudience('technical')">Technical</button>
                <button class="audience-btn" onclick="filterByAudience('researchers')">Researchers</button>
                <button class="audience-btn" onclick="filterByAudience('financial')">Financial</button>
            </div>
        </div>
        
        <div class="stats" id="demoStats">
            <h3>📊 Demo Statistics</h3>
            <p><strong>18</strong> working demonstrations available</p>
            <p>Filtered for: <strong>All Audiences</strong></p>
        </div>
        
        <div class="demo-categories" id="demoCategories">
            <!-- Security Demos -->
            <div class="category" data-audiences="investors,enterprise,government,technical">
                <h3>🔮 Quantum Security Demos</h3>
                
                <div class="demo-item featured-demo" onclick="launchDemo('comphy-field-demo.html')">
                    <div class="demo-name">🏆 Comphy-Field Self-Destructing Data System</div>
                    <div class="demo-description">Revolutionary "Steal It, and It Turns to Dust" security</div>
                    <div class="demo-audience">Audience: investors, enterprise, government</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('comphy-field-demo.html')">Launch</button>
                </div>
                
                <div class="demo-item" onclick="launchDemo('../src/novashield/NovaShield_TraceGuard_MVP.py')">
                    <div class="demo-name">NovaShield TraceGuard</div>
                    <div class="demo-description">Advanced threat detection and response</div>
                    <div class="demo-audience">Audience: technical, security</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('../src/novashield/NovaShield_TraceGuard_MVP.py')">Launch</button>
                </div>
                
                <div class="demo-item" onclick="launchDemo('../public/novavision-security-demo.html')">
                    <div class="demo-name">NovaVision Security</div>
                    <div class="demo-description">Comprehensive security monitoring</div>
                    <div class="demo-audience">Audience: enterprise, technical</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('../public/novavision-security-demo.html')">Launch</button>
                </div>
            </div>
            
            <!-- AI & Consciousness Demos -->
            <div class="category" data-audiences="researchers,technical,government">
                <h3>🧠 AI & Consciousness Demos</h3>
                
                <div class="demo-item" onclick="launchDemo('../coherence-reality-systems/ai-alignment-demo/')">
                    <div class="demo-name">NovaAlign Studio</div>
                    <div class="demo-description">AI alignment and consciousness validation</div>
                    <div class="demo-audience">Audience: researchers, technical, government</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('../coherence-reality-systems/ai-alignment-demo/')">Launch</button>
                </div>
                
                <div class="demo-item" onclick="launchDemo('../trinity_visualization/')">
                    <div class="demo-name">Trinity Consciousness Engine</div>
                    <div class="demo-description">Triadic consciousness processing</div>
                    <div class="demo-audience">Audience: researchers, technical</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('../trinity_visualization/')">Launch</button>
                </div>
                
                <div class="demo-item" onclick="launchDemo('../advanced_consciousness_demo.py')">
                    <div class="demo-name">Advanced Consciousness Demo</div>
                    <div class="demo-description">Comprehensive consciousness testing</div>
                    <div class="demo-audience">Audience: researchers, technical</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('../advanced_consciousness_demo.py')">Launch</button>
                </div>
            </div>
            
            <!-- Enterprise & Business Demos -->
            <div class="category" data-audiences="enterprise,financial,investors">
                <h3>🏢 Enterprise & Business Demos</h3>
                
                <div class="demo-item" onclick="launchDemo('../NOVAACTUARY-DEMO-COMPLETE.md')">
                    <div class="demo-name">NovaActuary Risk Assessment</div>
                    <div class="demo-description">AI-powered insurance and risk analysis</div>
                    <div class="demo-audience">Audience: enterprise, financial, investors</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('../NOVAACTUARY-DEMO-COMPLETE.md')">Launch</button>
                </div>
                
                <div class="demo-item" onclick="launchDemo('partner-ecosystem.html')">
                    <div class="demo-name">Partner Ecosystem Portal</div>
                    <div class="demo-description">Strategic partnership management</div>
                    <div class="demo-audience">Audience: partners, enterprise</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('partner-ecosystem.html')">Launch</button>
                </div>
                
                <div class="demo-item" onclick="launchDemo('../NovaConnect-Testing-Summary.md')">
                    <div class="demo-name">NovaConnect Testing Suite</div>
                    <div class="demo-description">Universal API connectivity platform</div>
                    <div class="demo-audience">Audience: technical, enterprise</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('../NovaConnect-Testing-Summary.md')">Launch</button>
                </div>
            </div>
            
            <!-- Research & Development Demos -->
            <div class="category" data-audiences="researchers,academic">
                <h3>🔬 Research & Development Demos</h3>
                
                <div class="demo-item" onclick="launchDemo('../unified-field-theory.html')">
                    <div class="demo-name">UUFT Unified Field Theory</div>
                    <div class="demo-description">Universal physics simulations</div>
                    <div class="demo-audience">Audience: researchers, academic</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('../unified-field-theory.html')">Launch</button>
                </div>
                
                <div class="demo-item" onclick="launchDemo('../three_body_problem_solver.py')">
                    <div class="demo-name">Three Body Problem Solver</div>
                    <div class="demo-description">Revolutionary physics solution</div>
                    <div class="demo-audience">Audience: researchers, academic</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('../three_body_problem_solver.py')">Launch</button>
                </div>
                
                <div class="demo-item" onclick="launchDemo('../Mathematical_Consciousness_Proof.py')">
                    <div class="demo-name">Mathematical Consciousness Proof</div>
                    <div class="demo-description">Consciousness mathematics validation</div>
                    <div class="demo-audience">Audience: researchers, academic</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('../Mathematical_Consciousness_Proof.py')">Launch</button>
                </div>
            </div>
            
            <!-- Financial & Trading Demos -->
            <div class="category" data-audiences="financial,investors">
                <h3>💰 Financial & Trading Demos</h3>
                
                <div class="demo-item" onclick="launchDemo('../coherence-reality-systems/chaeonix-divine-dashboard/')">
                    <div class="demo-name">CHAEONIX Divine Dashboard</div>
                    <div class="demo-description">Consciousness-based trading platform</div>
                    <div class="demo-audience">Audience: financial, investors</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('../coherence-reality-systems/chaeonix-divine-dashboard/')">Launch</button>
                </div>
                
                <div class="demo-item" onclick="launchDemo('../src/wall_street_oracle/')">
                    <div class="demo-name">Wall Street Oracle</div>
                    <div class="demo-description">Predictive market analysis</div>
                    <div class="demo-audience">Audience: financial, investors</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('../src/wall_street_oracle/')">Launch</button>
                </div>
                
                <div class="demo-item" onclick="launchDemo('../revenue-calculator.md')">
                    <div class="demo-name">Revenue Calculator</div>
                    <div class="demo-description">Financial forecasting and projections</div>
                    <div class="demo-audience">Audience: investors, financial</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('../revenue-calculator.md')">Launch</button>
                </div>
            </div>
            
            <!-- Interactive Presentations -->
            <div class="category" data-audiences="investors,partners,enterprise,executives">
                <h3>🎮 Interactive Presentations</h3>
                
                <div class="demo-item" onclick="launchDemo('technology-roadmap.html')">
                    <div class="demo-name">Technology Roadmap</div>
                    <div class="demo-description">NovaFuse development timeline</div>
                    <div class="demo-audience">Audience: investors, partners, enterprise</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('technology-roadmap.html')">Launch</button>
                </div>
                
                <div class="demo-item" onclick="launchDemo('competitor-matrix.html')">
                    <div class="demo-name">Competitor Analysis</div>
                    <div class="demo-description">Market positioning matrix</div>
                    <div class="demo-audience">Audience: investors, enterprise</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('competitor-matrix.html')">Launch</button>
                </div>
                
                <div class="demo-item" onclick="launchDemo('../NovaFuse-Executive-Summary-20250720.html')">
                    <div class="demo-name">Executive Summary</div>
                    <div class="demo-description">High-level NovaFuse overview</div>
                    <div class="demo-audience">Audience: executives, investors</div>
                    <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('../NovaFuse-Executive-Summary-20250720.html')">Launch</button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        let currentAudience = 'all';
        
        function filterByAudience(audience) {
            currentAudience = audience;
            
            // Update button states
            document.querySelectorAll('.audience-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // Filter categories
            const categories = document.querySelectorAll('.category');
            let visibleDemos = 0;
            
            categories.forEach(category => {
                const audiences = category.getAttribute('data-audiences').split(',');
                const shouldShow = audience === 'all' || audiences.some(aud => aud.trim() === audience);
                
                if (shouldShow) {
                    category.style.display = 'block';
                    visibleDemos += category.querySelectorAll('.demo-item').length;
                } else {
                    category.style.display = 'none';
                }
            });
            
            // Update stats
            document.getElementById('demoStats').innerHTML = `
                <h3>📊 Demo Statistics</h3>
                <p><strong>${visibleDemos}</strong> working demonstrations available</p>
                <p>Filtered for: <strong>${audience === 'all' ? 'All Audiences' : audience}</strong></p>
            `;
        }
        
        function launchDemo(demoPath) {
            if (demoPath.endsWith('.html')) {
                // Open HTML files directly
                window.open(demoPath, '_blank');
            } else if (demoPath.endsWith('.py')) {
                alert(`Python Demo: ${demoPath}\n\nTo run this demo, execute:\npython ${demoPath}`);
            } else if (demoPath.endsWith('.md')) {
                // Open markdown files (will depend on browser)
                window.open(demoPath, '_blank');
            } else if (demoPath.endsWith('/')) {
                // Directory - try to find index file
                const indexPath = demoPath + 'index.html';
                window.open(indexPath, '_blank');
            } else {
                alert(`Demo: ${demoPath}\n\nThis demo requires manual launch. Check the demo documentation for specific instructions.`);
            }
        }
    </script>
</body>
</html>
