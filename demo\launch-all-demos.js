/**
 * NovaFuse Demo Launcher - Central Hub for All Demonstrations
 * Provides easy access to all working demos and dashboards
 * 
 * Author: <PERSON>, NovaFuse Technologies
 * Date: 2025-07-29
 */

const express = require('express');
const path = require('path');
const fs = require('fs');
const { spawn } = require('child_process');

const app = express();
const PORT = 8888; // Unique port to avoid conflicts

// Demo categories and their configurations
const DEMO_CATEGORIES = {
    security: {
        name: "🔮 Quantum Security Demos",
        demos: [
            {
                name: "Comphy-Field Self-Destructing Data",
                file: "comphy-field-demo.html",
                type: "html",
                description: "Revolutionary 'Steal It, and It Turns to Dust' security",
                audience: ["investors", "enterprise", "government"]
            },
            {
                name: "NovaShield TraceGuard",
                file: "../src/novashield/NovaShield_TraceGuard_MVP.py",
                type: "python",
                description: "Advanced threat detection and response",
                audience: ["technical", "security"]
            },
            {
                name: "NovaVision Security",
                file: "../public/novavision-security-demo.html",
                type: "html",
                description: "Comprehensive security monitoring",
                audience: ["enterprise", "technical"]
            }
        ]
    },
    consciousness: {
        name: "🧠 AI & Consciousness Demos",
        demos: [
            {
                name: "NovaAlign Studio",
                file: "../coherence-reality-systems/ai-alignment-demo/",
                type: "directory",
                description: "AI alignment and consciousness validation",
                audience: ["researchers", "technical", "government"]
            },
            {
                name: "Trinity Consciousness Engine",
                file: "../trinity_visualization/",
                type: "directory",
                description: "Triadic consciousness processing",
                audience: ["researchers", "technical"]
            },
            {
                name: "Advanced Consciousness Demo",
                file: "../advanced_consciousness_demo.py",
                type: "python",
                description: "Comprehensive consciousness testing",
                audience: ["researchers", "technical"]
            }
        ]
    },
    enterprise: {
        name: "🏢 Enterprise & Business Demos",
        demos: [
            {
                name: "NovaActuary Risk Assessment",
                file: "../NOVAACTUARY-DEMO-COMPLETE.md",
                type: "documentation",
                description: "AI-powered insurance and risk analysis",
                audience: ["enterprise", "financial", "investors"]
            },
            {
                name: "Partner Ecosystem Portal",
                file: "partner-ecosystem.html",
                type: "html",
                description: "Strategic partnership management",
                audience: ["partners", "enterprise"]
            },
            {
                name: "NovaConnect Testing Suite",
                file: "../NovaConnect-Testing-Summary.md",
                type: "documentation",
                description: "Universal API connectivity platform",
                audience: ["technical", "enterprise"]
            }
        ]
    },
    research: {
        name: "🔬 Research & Development Demos",
        demos: [
            {
                name: "UUFT Unified Field Theory",
                file: "../unified-field-theory.html",
                type: "html",
                description: "Universal physics simulations",
                audience: ["researchers", "academic"]
            },
            {
                name: "Three Body Problem Solver",
                file: "../three_body_problem_solver.py",
                type: "python",
                description: "Revolutionary physics solution",
                audience: ["researchers", "academic"]
            },
            {
                name: "Mathematical Consciousness Proof",
                file: "../Mathematical_Consciousness_Proof.py",
                type: "python",
                description: "Consciousness mathematics validation",
                audience: ["researchers", "academic"]
            }
        ]
    },
    financial: {
        name: "💰 Financial & Trading Demos",
        demos: [
            {
                name: "CHAEONIX Divine Dashboard",
                file: "../coherence-reality-systems/chaeonix-divine-dashboard/",
                type: "directory",
                description: "Consciousness-based trading platform",
                audience: ["financial", "investors"]
            },
            {
                name: "Wall Street Oracle",
                file: "../src/wall_street_oracle/",
                type: "directory",
                description: "Predictive market analysis",
                audience: ["financial", "investors"]
            },
            {
                name: "Revenue Calculator",
                file: "../revenue-calculator.md",
                type: "documentation",
                description: "Financial forecasting and projections",
                audience: ["investors", "financial"]
            }
        ]
    },
    presentations: {
        name: "🎮 Interactive Presentations",
        demos: [
            {
                name: "Technology Roadmap",
                file: "technology-roadmap.html",
                type: "html",
                description: "NovaFuse development timeline",
                audience: ["investors", "partners", "enterprise"]
            },
            {
                name: "Competitor Analysis",
                file: "competitor-matrix.html",
                type: "html",
                description: "Market positioning matrix",
                audience: ["investors", "enterprise"]
            },
            {
                name: "Executive Summary",
                file: "../NovaFuse-Executive-Summary-20250720.html",
                type: "html",
                description: "High-level NovaFuse overview",
                audience: ["executives", "investors"]
            }
        ]
    }
};

// Middleware
app.use(express.json());
app.use(express.static(__dirname));
app.use('/public', express.static(path.join(__dirname, '../public')));
app.use('/src', express.static(path.join(__dirname, '../src')));

/**
 * Main demo hub page
 */
app.get('/', (req, res) => {
    const hubHTML = generateDemoHub();
    res.send(hubHTML);
});

/**
 * API to get all demo categories
 */
app.get('/api/demos', (req, res) => {
    const audience = req.query.audience;
    let filteredCategories = DEMO_CATEGORIES;
    
    if (audience) {
        filteredCategories = {};
        for (const [categoryKey, category] of Object.entries(DEMO_CATEGORIES)) {
            const filteredDemos = category.demos.filter(demo => 
                demo.audience.includes(audience)
            );
            if (filteredDemos.length > 0) {
                filteredCategories[categoryKey] = {
                    ...category,
                    demos: filteredDemos
                };
            }
        }
    }
    
    res.json({
        success: true,
        categories: filteredCategories,
        totalDemos: Object.values(filteredCategories).reduce((sum, cat) => sum + cat.demos.length, 0)
    });
});

/**
 * API to launch a specific demo
 */
app.post('/api/launch/:category/:demoIndex', async (req, res) => {
    try {
        const { category, demoIndex } = req.params;
        const categoryData = DEMO_CATEGORIES[category];
        
        if (!categoryData) {
            return res.status(404).json({ success: false, error: 'Category not found' });
        }
        
        const demo = categoryData.demos[parseInt(demoIndex)];
        if (!demo) {
            return res.status(404).json({ success: false, error: 'Demo not found' });
        }
        
        const launchResult = await launchDemo(demo);
        
        res.json({
            success: true,
            demo: demo.name,
            result: launchResult
        });
        
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * Generate the main demo hub HTML
 */
function generateDemoHub() {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NovaFuse Demo Central Hub</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: #ffffff;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            backdrop-filter: blur(10px);
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #00d4ff, #ff00ff, #ffff00);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .audience-filter {
            margin: 20px 0;
            text-align: center;
        }
        
        .audience-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: #fff;
            padding: 8px 16px;
            margin: 5px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .audience-btn:hover, .audience-btn.active {
            background: #00d4ff;
            color: #000;
        }
        
        .demo-categories {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
        }
        
        .category {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .category h3 {
            color: #00d4ff;
            margin-bottom: 20px;
            font-size: 1.3em;
        }
        
        .demo-item {
            background: rgba(255, 255, 255, 0.03);
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            border: 1px solid rgba(255, 255, 255, 0.1);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .demo-item:hover {
            background: rgba(255, 255, 255, 0.08);
            transform: translateY(-2px);
        }
        
        .demo-name {
            font-weight: bold;
            color: #fff;
            margin-bottom: 5px;
        }
        
        .demo-description {
            color: #ccc;
            font-size: 0.9em;
            margin-bottom: 8px;
        }
        
        .demo-audience {
            font-size: 0.8em;
            color: #00d4ff;
        }
        
        .launch-btn {
            background: linear-gradient(45deg, #00ff88, #00d4ff);
            color: #000;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            float: right;
            margin-top: 10px;
        }
        
        .launch-btn:hover {
            transform: scale(1.05);
        }
        
        .stats {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎬 NovaFuse Demo Central Hub</h1>
            <p>Every Working Demo and Dashboard in One Place</p>
            
            <div class="audience-filter">
                <button class="audience-btn active" onclick="filterByAudience('all')">All Demos</button>
                <button class="audience-btn" onclick="filterByAudience('investors')">Investors</button>
                <button class="audience-btn" onclick="filterByAudience('enterprise')">Enterprise</button>
                <button class="audience-btn" onclick="filterByAudience('technical')">Technical</button>
                <button class="audience-btn" onclick="filterByAudience('researchers')">Researchers</button>
                <button class="audience-btn" onclick="filterByAudience('financial')">Financial</button>
            </div>
        </div>
        
        <div class="stats" id="demoStats">
            Loading demo statistics...
        </div>
        
        <div class="demo-categories" id="demoCategories">
            Loading demos...
        </div>
    </div>
    
    <script>
        let currentAudience = 'all';
        
        async function loadDemos(audience = 'all') {
            try {
                const url = audience === 'all' ? '/api/demos' : \`/api/demos?audience=\${audience}\`;
                const response = await fetch(url);
                const data = await response.json();
                
                if (data.success) {
                    displayDemos(data.categories);
                    updateStats(data.totalDemos);
                }
            } catch (error) {
                console.error('Error loading demos:', error);
            }
        }
        
        function displayDemos(categories) {
            const container = document.getElementById('demoCategories');
            container.innerHTML = '';
            
            for (const [categoryKey, category] of Object.entries(categories)) {
                const categoryDiv = document.createElement('div');
                categoryDiv.className = 'category';
                
                let demosHTML = '';
                category.demos.forEach((demo, index) => {
                    demosHTML += \`
                        <div class="demo-item" onclick="launchDemo('\${categoryKey}', \${index})">
                            <div class="demo-name">\${demo.name}</div>
                            <div class="demo-description">\${demo.description}</div>
                            <div class="demo-audience">Audience: \${demo.audience.join(', ')}</div>
                            <button class="launch-btn" onclick="event.stopPropagation(); launchDemo('\${categoryKey}', \${index})">Launch</button>
                        </div>
                    \`;
                });
                
                categoryDiv.innerHTML = \`
                    <h3>\${category.name}</h3>
                    \${demosHTML}
                \`;
                
                container.appendChild(categoryDiv);
            }
        }
        
        function updateStats(totalDemos) {
            document.getElementById('demoStats').innerHTML = \`
                <h3>📊 Demo Statistics</h3>
                <p><strong>\${totalDemos}</strong> working demonstrations available</p>
                <p>Filtered for: <strong>\${currentAudience === 'all' ? 'All Audiences' : currentAudience}</strong></p>
            \`;
        }
        
        function filterByAudience(audience) {
            currentAudience = audience;
            
            // Update button states
            document.querySelectorAll('.audience-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            loadDemos(audience);
        }
        
        async function launchDemo(category, demoIndex) {
            try {
                const response = await fetch(\`/api/launch/\${category}/\${demoIndex}\`, {
                    method: 'POST'
                });
                
                const result = await response.json();
                
                if (result.success) {
                    alert(\`Demo "\${result.demo}" launched successfully!\`);
                } else {
                    alert(\`Error launching demo: \${result.error}\`);
                }
            } catch (error) {
                alert(\`Error: \${error.message}\`);
            }
        }
        
        // Initialize
        loadDemos();
    </script>
</body>
</html>
    `;
}

/**
 * Launch a specific demo based on its type
 */
async function launchDemo(demo) {
    const demoPath = path.resolve(__dirname, demo.file);
    
    switch (demo.type) {
        case 'html':
            // For HTML files, we'll return the URL to open
            return {
                type: 'url',
                url: `http://localhost:${PORT}/${demo.file}`,
                message: 'Open the provided URL in your browser'
            };
            
        case 'python':
            // For Python files, spawn a Python process
            return new Promise((resolve, reject) => {
                const pythonProcess = spawn('python', [demoPath]);
                
                pythonProcess.on('spawn', () => {
                    resolve({
                        type: 'process',
                        message: 'Python demo launched successfully',
                        pid: pythonProcess.pid
                    });
                });
                
                pythonProcess.on('error', (error) => {
                    reject(new Error(`Failed to launch Python demo: ${error.message}`));
                });
            });
            
        case 'directory':
            // For directories, check for common entry points
            const entryPoints = ['index.html', 'index.js', 'server.js', 'app.js'];
            for (const entry of entryPoints) {
                const entryPath = path.join(demoPath, entry);
                if (fs.existsSync(entryPath)) {
                    return {
                        type: 'directory',
                        entry: entry,
                        path: entryPath,
                        message: `Found entry point: ${entry}`
                    };
                }
            }
            return {
                type: 'directory',
                message: 'Directory demo - manual navigation required',
                path: demoPath
            };
            
        case 'documentation':
            return {
                type: 'documentation',
                path: demoPath,
                message: 'Documentation file - open with text editor or markdown viewer'
            };
            
        default:
            return {
                type: 'unknown',
                message: 'Demo type not recognized - manual launch required'
            };
    }
}

// Start the server
app.listen(PORT, () => {
    console.log('\n🎬 NOVAFUSE DEMO CENTRAL HUB STARTED');
    console.log('=====================================');
    console.log(`🌐 Demo Hub: http://localhost:${PORT}`);
    console.log(`📊 API Base: http://localhost:${PORT}/api`);
    console.log(`🎯 Total Categories: ${Object.keys(DEMO_CATEGORIES).length}`);
    console.log(`🎪 Total Demos: ${Object.values(DEMO_CATEGORIES).reduce((sum, cat) => sum + cat.demos.length, 0)}`);
    console.log('\n🚀 All NovaFuse demos and dashboards are now accessible!');
    console.log('Press Ctrl+C to stop the demo hub');
});

module.exports = { app, DEMO_CATEGORIES };
