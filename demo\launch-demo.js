/**
 * Demo Launcher for Comphy-Field Self-Destructing Data System
 * Launches interactive web demo with live backend integration
 * 
 * Author: <PERSON>, NovaFuse Technologies
 * Date: 2025-07-29
 */

const express = require('express');
const path = require('path');
const { ComphyDocumentFactory } = require('../src/comphyology/security/ComphyLockedDocument');

const app = express();
const PORT = 3000;

// Middleware
app.use(express.json());
app.use(express.static(path.join(__dirname)));

// Demo state
let demoFactory = null;
let demoDocument = null;

/**
 * Initialize demo backend
 */
function initializeDemo() {
    console.log('🔮 Initializing Comphy-Field Demo Backend...');
    
    demoFactory = new ComphyDocumentFactory();
    
    console.log('✅ Demo backend initialized');
    console.log('🌐 Demo available at: http://localhost:3000');
}

/**
 * API endpoint to create demo document
 */
app.post('/api/create-document', async (req, res) => {
    try {
        const { content, title, classification } = req.body;
        
        console.log(`📄 Creating demo document: ${title}`);
        
        const docResult = await demoFactory.createDocument(content, {
            title: title || 'Demo Document',
            classification: classification || 'CONFIDENTIAL',
            coherenceThreshold: 0.95
        });
        
        demoDocument = docResult;
        
        res.json({
            success: true,
            document_id: docResult.document_id,
            message: 'Demo document created successfully'
        });
        
    } catch (error) {
        console.error('❌ Error creating demo document:', error.message);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * API endpoint for authorized access simulation
 */
app.post('/api/authorized-access', async (req, res) => {
    try {
        if (!demoDocument) {
            return res.status(400).json({
                success: false,
                error: 'No demo document available'
            });
        }
        
        console.log('✅ Simulating authorized access...');
        
        const authorizedContext = {
            entity_id: '<EMAIL>',
            field_readings: {
                field_signature: 'NOVAFUSE_LAB_ALPHA_FIELD_001',
                coherence_level: 0.97,
                resonance_frequency: 314.159,
                field_strength: 0.082,
                temporal_stability: 0.95
            },
            source_signature: 'authorized_workstation_001',
            location: 'NovaFuse_Lab_Alpha'
        };
        
        const accessResult = await demoFactory.accessDocument(
            demoDocument.document_id,
            demoDocument.comphy_document,
            authorizedContext
        );
        
        res.json({
            success: accessResult.success,
            result: accessResult,
            scenario: 'authorized_access'
        });
        
    } catch (error) {
        console.error('❌ Error in authorized access:', error.message);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * API endpoint for hacker attempt simulation
 */
app.post('/api/hacker-attempt', async (req, res) => {
    try {
        if (!demoDocument) {
            return res.status(400).json({
                success: false,
                error: 'No demo document available'
            });
        }
        
        console.log('🚨 Simulating hacker attempt...');
        
        const hackerContext = {
            entity_id: '<EMAIL>',
            field_readings: {
                field_signature: 'FAKE_SIGNATURE',
                coherence_level: 0.1,
                resonance_frequency: 50,
                field_strength: 0.001
            },
            source_signature: 'unknown_system',
            location: 'Remote_Location'
        };
        
        const accessResult = await demoFactory.accessDocument(
            demoDocument.document_id,
            demoDocument.comphy_document,
            hackerContext
        );
        
        res.json({
            success: false, // Hacker should always fail
            result: accessResult,
            scenario: 'hacker_attempt'
        });
        
    } catch (error) {
        console.error('❌ Error in hacker simulation:', error.message);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * API endpoint for email copy simulation
 */
app.post('/api/email-copy', async (req, res) => {
    try {
        if (!demoDocument) {
            return res.status(400).json({
                success: false,
                error: 'No demo document available'
            });
        }
        
        console.log('📧 Simulating email copy attempt...');
        
        const emailContext = {
            recipient: '<EMAIL>',
            method: 'email_attachment',
            email_client: 'outlook',
            timestamp: Date.now()
        };
        
        const copyResult = await demoFactory.simulateEmailCopy(
            demoDocument.document_id,
            demoDocument.comphy_document,
            emailContext
        );
        
        res.json({
            success: false, // Copy should always fail
            result: copyResult,
            scenario: 'email_copy'
        });
        
    } catch (error) {
        console.error('❌ Error in email copy simulation:', error.message);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * API endpoint to get demo status
 */
app.get('/api/demo-status', (req, res) => {
    res.json({
        demo_active: demoFactory !== null,
        document_created: demoDocument !== null,
        document_id: demoDocument?.document_id || null,
        factory_status: demoFactory ? 'initialized' : 'not_initialized'
    });
});

/**
 * API endpoint to reset demo
 */
app.post('/api/reset-demo', (req, res) => {
    console.log('🔄 Resetting demo...');
    
    demoDocument = null;
    // Keep factory initialized for performance
    
    res.json({
        success: true,
        message: 'Demo reset successfully'
    });
});

/**
 * Serve the main demo page
 */
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'comphy-field-demo.html'));
});

/**
 * Health check endpoint
 */
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        demo_system: 'Comphy-Field Self-Destructing Data System',
        version: '1.0.0',
        author: 'David Nigel Irvin, NovaFuse Technologies'
    });
});

/**
 * Start the demo server
 */
function startDemo() {
    initializeDemo();
    
    app.listen(PORT, () => {
        console.log('\n🎬 COMPHY-FIELD DEMO SERVER STARTED');
        console.log('===================================');
        console.log(`🌐 Demo URL: http://localhost:${PORT}`);
        console.log(`🔧 API Base: http://localhost:${PORT}/api`);
        console.log(`💚 Health Check: http://localhost:${PORT}/health`);
        console.log('\n🔮 "Steal It, and It Turns to Dust" - Demo Ready!');
        console.log('\nPress Ctrl+C to stop the demo server');
    });
}

// Handle graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down demo server...');
    console.log('✅ Demo server stopped');
    process.exit(0);
});

// Start the demo if run directly
if (require.main === module) {
    startDemo();
}

module.exports = {
    app,
    startDemo,
    initializeDemo
};
